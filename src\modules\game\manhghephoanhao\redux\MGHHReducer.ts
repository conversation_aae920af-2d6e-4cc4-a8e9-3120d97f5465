import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {GameConfig, Question} from '../models/models';
import {loadInitData} from './MGHHAsyncThunk';

interface State {
  dataListQuestion: Question[];
  listQuestions: Question[];
  currentQuestion: Question | null;
  currentLevel: number;
  totalQuestion: number;
  questionDone: number;
  config: GameConfig;
  loadData: boolean;
}

const initialState: State = {
  dataListQuestion: [],
  listQuestions: [],
  currentQuestion: null,
  currentLevel: 0,
  totalQuestion: 0,
  questionDone: 0,
  config: {
    configLv1: {},
    configLv2: {},
    configLv3: {},
  },
  loadData: false,
};

interface SetDataPayload {
  stateName: keyof State;
  value: any;
}

export const MGHHReducer = createSlice({
  name: 'MGHHReducer',
  initialState,
  reducers: {
    setData(state, action: PayloadAction<SetDataPayload>) {
      const {stateName, value} = action.payload;
      state[stateName] = value;
    },
    startGame: state => {
      debugger
      state.currentLevel = 1;
      const questionLevel = state.dataListQuestion.filter(
        question => question.level === state.currentLevel,
      );
      state.listQuestions = questionLevel;
      state.currentQuestion = questionLevel[0];
      state.totalQuestion = questionLevel.length;
      state.questionDone = 0;
      state.loadData = true;
    },
    nextQuestion: state => {
      state.currentQuestion = state.listQuestions[state.questionDone];
    },
    nextLevel: state => {
      const nextLevel = state.currentLevel + 1;
      const questionLevel = state.dataListQuestion.filter(
        question => question.level === nextLevel,
      );
      state.currentLevel = nextLevel;
      state.listQuestions = questionLevel;
      state.currentQuestion = questionLevel[0];
      state.questionDone = 0;
      state.totalQuestion = questionLevel.length;
    },
    restartLevel: state => {
      state.currentQuestion = state.listQuestions[0];
      state.questionDone = 0;
    },
    restartGame: state => {
      state.currentLevel = 0;
      state.listQuestions = [];
      state.currentQuestion = null;
      state.totalQuestion = 0;
      state.questionDone = 0;
    },
  },
  extraReducers: builder => {
    builder.addCase(loadInitData.fulfilled, (state, action) => {
      debugger
      state.config = action.payload.gameConfig;
      state.dataListQuestion = action.payload.questions;
      state.loadData = true;
    });
  },
});

export const {
  setData,
  startGame,
  nextQuestion,
  nextLevel,
  restartLevel,
  restartGame,
} = MGHHReducer.actions;

export default MGHHReducer.reducer;
